package com.intensivereading.app.ui.recording;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.intensivereading.app.R;
import com.intensivereading.app.audio.manager.AudioManager;
import com.intensivereading.app.databinding.FragmentRecordingListBinding;
import com.intensivereading.app.model.RecordingData;
import com.intensivereading.app.repository.DataRepository;

import java.util.List;

/**
 * 学习记录Fragment
 */
public class RecordingListFragment extends Fragment implements 
    RecordingListAdapter.OnRecordingClickListener,
    AudioManager.AudioManagerListener {
    
    private FragmentRecordingListBinding binding;
    private DataRepository dataRepository;
    private AudioManager audioManager;
    private RecordingListAdapter adapter;
    private List<RecordingData> recordingList;
    private RecordingData currentPlayingRecording;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化数据仓库和音频管理器
        dataRepository = DataRepository.getInstance(requireContext());
        audioManager = new AudioManager(requireContext());
        audioManager.setListener(this);
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        binding = FragmentRecordingListBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 设置返回按钮
        binding.backButton.setOnClickListener(v -> {
            Navigation.findNavController(v).popBackStack();
        });
        
        // 设置RecyclerView
        setupRecyclerView();
        
        // 加载录音数据
        loadRecordingData();
    }
    
    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        binding.recordingRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new RecordingListAdapter(requireContext(), recordingList);
        adapter.setOnRecordingClickListener(this);
        binding.recordingRecyclerView.setAdapter(adapter);
    }
    
    /**
     * 加载录音数据
     */
    private void loadRecordingData() {
        // 显示加载状态
        showLoadingState();

        // 在后台线程加载数据
        new Thread(() -> {
            try {
                List<RecordingData> data = dataRepository.getAllRecordingData();

                // 回到主线程更新UI
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        recordingList = data;
                        hideLoadingState();

                        if (recordingList.isEmpty()) {
                            // 显示空状态
                            binding.recordingRecyclerView.setVisibility(View.GONE);
                            binding.emptyStateLayout.setVisibility(View.VISIBLE);
                        } else {
                            // 显示录音列表
                            binding.recordingRecyclerView.setVisibility(View.VISIBLE);
                            binding.emptyStateLayout.setVisibility(View.GONE);

                            if (adapter != null) {
                                adapter.updateData(recordingList);
                            }
                        }
                    });
                }
            } catch (Exception e) {
                // 处理错误
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        hideLoadingState();
                        showErrorState(getString(R.string.loading_recordings, e.getMessage()));
                    });
                }
            }
        }).start();
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        if (binding != null) {
            binding.recordingRecyclerView.setVisibility(View.GONE);
            binding.emptyStateLayout.setVisibility(View.GONE);
            // 可以添加进度条显示
        }
    }

    /**
     * 隐藏加载状态
     */
    private void hideLoadingState() {
        // 加载状态处理在loadRecordingData中完成
    }

    /**
     * 显示错误状态
     */
    private void showErrorState(String errorMessage) {
        if (binding != null) {
            binding.recordingRecyclerView.setVisibility(View.GONE);
            binding.emptyStateLayout.setVisibility(View.VISIBLE);
            Toast.makeText(requireContext(), errorMessage, Toast.LENGTH_LONG).show();
        }
    }
    
    @Override
    public void onPlayClick(RecordingData recording) {
        if (recording.hasRecording() && recording.getAudioPath() != null) {
            // 停止当前播放
            if (audioManager.isPlaying()) {
                audioManager.stopPlayback();
            }

            // 播放选中的录音
            currentPlayingRecording = recording;
            boolean success = audioManager.playAudio(recording.getAudioPath());
            if (success && adapter != null) {
                adapter.setCurrentPlayingRecording(recording, true);
            }
        }
    }

    @Override
    public void onPauseClick(RecordingData recording) {
        if (audioManager.isPlaying()) {
            audioManager.pausePlayback();
            if (adapter != null) {
                adapter.setCurrentPlayingRecording(recording, false);
            }
        }
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        if (audioManager != null) {
            audioManager.release();
        }
        if (binding != null) {
            binding = null;
        }
    }
    
    // AudioManager.AudioManagerListener 实现
    @Override
    public void onRecordingStarted() {
        // 录音记录页面不需要录音功能
    }
    
    @Override
    public void onRecordingProgress(long duration) {
        // 录音记录页面不需要录音功能
    }
    
    @Override
    public void onRecordingStopped(String filePath, long duration) {
        // 录音记录页面不需要录音功能
    }
    
    @Override
    public void onRecordingError(String error) {
        // 录音记录页面不需要录音功能
    }
    
    @Override
    public void onPlaybackStarted() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (currentPlayingRecording != null && adapter != null) {
                    adapter.setCurrentPlayingRecording(currentPlayingRecording, true);
                    Toast.makeText(requireContext(),
                        getString(R.string.playing_title, currentPlayingRecording.getDisplayTitle()),
                        Toast.LENGTH_SHORT).show();
                }
            });
        }
    }
    
    @Override
    public void onPlaybackProgress(int currentPosition, int duration) {
        // 可以在这里更新播放进度
    }
    
    @Override
    public void onPlaybackPaused() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (currentPlayingRecording != null && adapter != null) {
                    adapter.setCurrentPlayingRecording(currentPlayingRecording, false);
                }
                Toast.makeText(requireContext(), getString(R.string.playback_paused), Toast.LENGTH_SHORT).show();
            });
        }
    }
    
    @Override
    public void onPlaybackCompleted() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                currentPlayingRecording = null;
                if (adapter != null) {
                    adapter.clearPlayingState();
                }
                Toast.makeText(requireContext(), getString(R.string.playback_completed), Toast.LENGTH_SHORT).show();
            });
        }
    }
    
    @Override
    public void onPlaybackError(String error) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                currentPlayingRecording = null;
                if (adapter != null) {
                    adapter.clearPlayingState();
                }
                Toast.makeText(requireContext(), getString(R.string.playback_error, error), Toast.LENGTH_SHORT).show();
            });
        }
    }
}
