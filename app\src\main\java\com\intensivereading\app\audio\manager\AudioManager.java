package com.intensivereading.app.audio.manager;

import android.content.Context;
import android.media.AudioFocusRequest;
import android.media.AudioAttributes;
import android.os.Build;
import android.util.Log;

import com.intensivereading.app.audio.player.AudioPlayer;
import com.intensivereading.app.audio.recorder.AudioRecorder;
import com.intensivereading.app.model.AudioState;
import com.intensivereading.app.repository.DataRepository;

/**
 * 音频管理器，统一管理录音和播放功能
 */
public class AudioManager implements 
    AudioRecorder.AudioRecorderListener, 
    AudioPlayer.AudioPlayerListener {
    
    private static final String TAG = "AudioManager";
    
    private Context context;
    private AudioRecorder audioRecorder;
    private AudioPlayer audioPlayer;
    private DataRepository dataRepository;
    private AudioManagerListener listener;
    private android.media.AudioManager systemAudioManager;
    private AudioFocusRequest audioFocusRequest;
    
    // 当前状态
    private String currentDayId;
    private int currentPageNumber;
    
    public interface AudioManagerListener {
        void onRecordingStarted();
        void onRecordingProgress(long duration);
        void onRecordingStopped(String filePath, long duration);
        void onRecordingError(String error);
        
        void onPlaybackStarted();
        void onPlaybackProgress(int currentPosition, int duration);
        void onPlaybackPaused();
        void onPlaybackCompleted();
        void onPlaybackError(String error);
    }
    
    public AudioManager(Context context) {
        this.context = context;
        this.audioRecorder = new AudioRecorder(context);
        this.audioPlayer = new AudioPlayer(context);
        this.dataRepository = DataRepository.getInstance(context);
        this.systemAudioManager = (android.media.AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        
        // 设置监听器
        audioRecorder.setListener(this);
        audioPlayer.setListener(this);
        
        // 初始化音频焦点请求
        initAudioFocusRequest();
    }
    
    public void setListener(AudioManagerListener listener) {
        this.listener = listener;
    }
    
    /**
     * 初始化音频焦点请求
     */
    private void initAudioFocusRequest() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build();
                
            audioFocusRequest = new AudioFocusRequest.Builder(android.media.AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener(focusChangeListener)
                .build();
        }
    }
    
    /**
     * 音频焦点变化监听器
     */
    private android.media.AudioManager.OnAudioFocusChangeListener focusChangeListener = 
        new android.media.AudioManager.OnAudioFocusChangeListener() {
        @Override
        public void onAudioFocusChange(int focusChange) {
            switch (focusChange) {
                case android.media.AudioManager.AUDIOFOCUS_LOSS:
                case android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                    // 失去音频焦点，暂停播放
                    if (audioPlayer.isPlaying()) {
                        audioPlayer.pause();
                    }
                    break;
                case android.media.AudioManager.AUDIOFOCUS_GAIN:
                    // 重新获得音频焦点，可以继续播放
                    break;
                case android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                    // 短暂失去焦点，降低音量
                    break;
            }
        }
    };
    
    /**
     * 请求音频焦点
     */
    private boolean requestAudioFocus() {
        int result;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            result = systemAudioManager.requestAudioFocus(audioFocusRequest);
        } else {
            result = systemAudioManager.requestAudioFocus(
                focusChangeListener,
                android.media.AudioManager.STREAM_MUSIC,
                android.media.AudioManager.AUDIOFOCUS_GAIN
            );
        }
        return result == android.media.AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
    }
    
    /**
     * 释放音频焦点
     */
    private void abandonAudioFocus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            systemAudioManager.abandonAudioFocusRequest(audioFocusRequest);
        } else {
            systemAudioManager.abandonAudioFocus(focusChangeListener);
        }
    }
    
    /**
     * 设置当前页面信息
     */
    public void setCurrentPage(String dayId, int pageNumber) {
        this.currentDayId = dayId;
        this.currentPageNumber = pageNumber;
    }
    
    /**
     * 开始录音
     */
    public boolean startRecording() {
        if (currentDayId == null) {
            Log.e(TAG, "当前页面信息未设置");
            return false;
        }
        
        // 停止播放
        if (audioPlayer.isPlaying()) {
            audioPlayer.stop();
        }
        
        return audioRecorder.startRecording(currentDayId, currentPageNumber);
    }
    
    /**
     * 停止录音
     */
    public boolean stopRecording() {
        return audioRecorder.stopRecording();
    }
    
    /**
     * 取消录音
     */
    public void cancelRecording() {
        audioRecorder.cancelRecording();
    }
    
    /**
     * 播放当前页面的音频
     */
    public boolean playCurrentPageAudio() {
        if (currentDayId == null) {
            Log.e(TAG, "当前页面信息未设置");
            return false;
        }
        
        // 停止录音
        if (audioRecorder.isRecording()) {
            audioRecorder.cancelRecording();
        }
        
        // 获取音频文件路径
        String audioPath = dataRepository.getDayData(currentDayId)
            .getPage(currentPageNumber).getAudioPath();
            
        if (audioPath == null || audioPath.isEmpty()) {
            if (listener != null) {
                listener.onPlaybackError("没有音频文件");
            }
            return false;
        }
        
        // 请求音频焦点
        if (!requestAudioFocus()) {
            if (listener != null) {
                listener.onPlaybackError("无法获取音频焦点");
            }
            return false;
        }
        
        return audioPlayer.play(audioPath);
    }

    /**
     * 播放指定路径的音频文件
     */
    public boolean playAudio(String audioPath) {
        if (audioPath == null || audioPath.isEmpty()) {
            if (listener != null) {
                listener.onPlaybackError("音频文件路径为空");
            }
            return false;
        }

        // 停止录音
        if (audioRecorder.isRecording()) {
            audioRecorder.cancelRecording();
        }

        // 请求音频焦点
        if (!requestAudioFocus()) {
            if (listener != null) {
                listener.onPlaybackError("无法获取音频焦点");
            }
            return false;
        }

        return audioPlayer.play(audioPath);
    }

    /**
     * 暂停播放
     */
    public void pausePlayback() {
        audioPlayer.pause();
    }
    
    /**
     * 停止播放
     */
    public void stopPlayback() {
        audioPlayer.stop();
        abandonAudioFocus();
    }
    
    /**
     * 获取录音状态
     */
    public AudioState getRecordingState() {
        return audioRecorder.getCurrentState();
    }
    
    /**
     * 获取播放状态
     */
    public AudioState getPlaybackState() {
        return audioPlayer.getCurrentState();
    }
    
    /**
     * 是否正在录音
     */
    public boolean isRecording() {
        return audioRecorder.isRecording();
    }
    
    /**
     * 是否正在播放
     */
    public boolean isPlaying() {
        return audioPlayer.isPlaying();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        audioRecorder.release();
        audioPlayer.release();
        abandonAudioFocus();
    }
    
    // AudioRecorder.AudioRecorderListener 实现
    @Override
    public void onRecordingStarted() {
        if (listener != null) {
            listener.onRecordingStarted();
        }
    }
    
    @Override
    public void onRecordingProgress(long duration) {
        if (listener != null) {
            listener.onRecordingProgress(duration);
        }
    }
    
    @Override
    public void onRecordingStopped(String filePath, long duration) {
        // 更新数据仓库
        dataRepository.updatePageAudioPath(currentDayId, currentPageNumber, filePath);
        
        if (listener != null) {
            listener.onRecordingStopped(filePath, duration);
        }
    }
    
    @Override
    public void onRecordingError(String error) {
        if (listener != null) {
            listener.onRecordingError(error);
        }
    }
    
    // AudioPlayer.AudioPlayerListener 实现
    @Override
    public void onPlaybackStarted() {
        if (listener != null) {
            listener.onPlaybackStarted();
        }
    }
    
    @Override
    public void onPlaybackProgress(int currentPosition, int duration) {
        if (listener != null) {
            listener.onPlaybackProgress(currentPosition, duration);
        }
    }
    
    @Override
    public void onPlaybackPaused() {
        if (listener != null) {
            listener.onPlaybackPaused();
        }
    }
    
    @Override
    public void onPlaybackCompleted() {
        abandonAudioFocus();
        if (listener != null) {
            listener.onPlaybackCompleted();
        }
    }
    
    @Override
    public void onPlaybackError(String error) {
        abandonAudioFocus();
        if (listener != null) {
            listener.onPlaybackError(error);
        }
    }
}
