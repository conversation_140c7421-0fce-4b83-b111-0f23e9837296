package com.intensivereading.app.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 表示一天的学习数据
 */
public class DayData {
    private String dayId;           // 天的标识符 (monday, tuesday, etc.)
    private String displayName;     // 显示名称 (周一, 周二, etc.)
    private int totalPages;         // 总页数，默认10页
    private List<PageData> pages;   // 页面数据列表
    
    public DayData(String dayId, String displayName) {
        this.dayId = dayId;
        this.displayName = displayName;
        this.totalPages = 10; // 默认10页
        this.pages = new ArrayList<>();
        
        // 初始化10个页面
        for (int i = 0; i < totalPages; i++) {
            pages.add(new PageData(i + 1));
        }
    }
    
    // Getters and Setters
    public String getDayId() {
        return dayId;
    }
    
    public void setDayId(String dayId) {
        this.dayId = dayId;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public int getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }
    
    public List<PageData> getPages() {
        return pages;
    }
    
    public void setPages(List<PageData> pages) {
        this.pages = pages;
    }
    
    public PageData getPage(int pageNumber) {
        if (pageNumber >= 1 && pageNumber <= pages.size()) {
            return pages.get(pageNumber - 1);
        }
        return null;
    }
}
