package com.intensivereading.app.ui.detail;

import android.os.Bundle;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;
import androidx.viewpager2.widget.ViewPager2;

import com.intensivereading.app.R;
import com.intensivereading.app.audio.manager.AudioManager;
import com.intensivereading.app.databinding.FragmentDetailBinding;
import com.intensivereading.app.model.DayData;
import com.intensivereading.app.model.PageData;
import com.intensivereading.app.repository.DataRepository;

import java.util.Locale;

/**
 * 详情页面Fragment
 */
public class DetailFragment extends Fragment implements AudioManager.AudioManagerListener {
    
    private FragmentDetailBinding binding;
    private DataRepository dataRepository;
    private AudioManager audioManager;
    private ImagePagerAdapter pagerAdapter;
    private GestureDetector gestureDetector;

    private String dayId;
    private DayData dayData;
    private int currentPagePosition = 0;
    private boolean isRecording = false;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 获取传递的参数
        if (getArguments() != null) {
            dayId = getArguments().getString("dayId");
        }
        
        // 初始化数据仓库和音频管理器
        dataRepository = DataRepository.getInstance(requireContext());
        audioManager = new AudioManager(requireContext());
        audioManager.setListener(this);
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        binding = FragmentDetailBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 获取天数据
        dayData = dataRepository.getDayData(dayId);
        if (dayData == null) {
            Toast.makeText(requireContext(), "数据加载失败", Toast.LENGTH_SHORT).show();
            Navigation.findNavController(view).popBackStack();
            return;
        }
        
        // 设置ViewPager2
        setupViewPager();

        // 设置按钮点击事件
        setupButtonListeners();

        // 设置手势检测器（用于返回功能）
        setupGestureDetector();

        // 更新UI状态
        updateUI();
    }
    
    /**
     * 设置ViewPager2
     */
    private void setupViewPager() {
        pagerAdapter = new ImagePagerAdapter(requireContext(), dayData.getPages());
        binding.viewPager.setAdapter(pagerAdapter);
        
        // 设置页面变化监听器
        binding.viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                currentPagePosition = position;
                updateUI();

                // 停止播放
                if (audioManager.isPlaying()) {
                    audioManager.stopPlayback();
                }

                // 更新音频管理器的当前页面
                audioManager.setCurrentPage(dayId, position + 1);
            }
        });
        
        // 设置初始页面
        audioManager.setCurrentPage(dayId, 1);
    }
    
    /**
     * 设置手势检测器
     */
    private void setupGestureDetector() {
        gestureDetector = new GestureDetector(requireContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onDoubleTap(MotionEvent e) {
                // 双击返回
                Navigation.findNavController(requireView()).popBackStack();
                return true;
            }
        });

        // 为ViewPager2设置触摸监听器
        binding.viewPager.setOnTouchListener((v, event) -> {
            gestureDetector.onTouchEvent(event);
            return false; // 返回false以允许ViewPager2正常处理滑动
        });
    }

    /**
     * 设置按钮监听器
     */
    private void setupButtonListeners() {
        // 返回按钮（虽然隐藏了，但保留代码以防需要）
        binding.backButton.setOnClickListener(v -> {
            Navigation.findNavController(v).popBackStack();
        });
        
        // 上一个按钮
        binding.previousButton.setOnClickListener(v -> {
            if (currentPagePosition > 0) {
                binding.viewPager.setCurrentItem(currentPagePosition - 1, true);
            }
        });
        
        // 下一个按钮
        binding.nextButton.setOnClickListener(v -> {
            if (currentPagePosition < dayData.getPages().size() - 1) {
                binding.viewPager.setCurrentItem(currentPagePosition + 1, true);
            }
        });
        
        // 播放按钮
        binding.playButton.setOnClickListener(v -> {
            if (audioManager.isPlaying()) {
                audioManager.pausePlayback();
            } else {
                audioManager.playCurrentPageAudio();
            }
        });
        
        // 录音按钮 - 长按录音
        binding.recordButton.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    // 开始录音
                    if (!isRecording) {
                        startRecording();
                    }
                    return true;
                    
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    // 停止录音
                    if (isRecording) {
                        stopRecording();
                    }
                    return true;
            }
            return false;
        });
    }
    
    /**
     * 开始录音
     */
    private void startRecording() {
        if (audioManager.startRecording()) {
            isRecording = true;
            binding.recordingOverlay.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 停止录音
     */
    private void stopRecording() {
        if (isRecording) {
            audioManager.stopRecording();
            isRecording = false;
            binding.recordingOverlay.setVisibility(View.GONE);
        }
    }
    
    /**
     * 更新UI状态
     */
    private void updateUI() {
        // 更新页面指示器
        binding.pageIndicator.setText(String.format(Locale.getDefault(), 
            "%d/%d", currentPagePosition + 1, dayData.getTotalPages()));
        
        // 更新上一个按钮状态
        binding.previousButton.setEnabled(currentPagePosition > 0);
        
        // 更新下一个按钮状态 - 只有当前页面有录音文件才能进入下一页
        PageData currentPage = dayData.getPage(currentPagePosition + 1);
        boolean hasAudioFile = currentPage != null && currentPage.hasAudio() &&
                              currentPage.getAudioPath() != null &&
                              !currentPage.getAudioPath().isEmpty();
        boolean isLastPage = currentPagePosition >= dayData.getTotalPages() - 1;
        binding.nextButton.setEnabled(hasAudioFile && !isLastPage);
        
        // 更新播放按钮状态
        boolean hasAudio = currentPage != null && currentPage.hasAudio();
        binding.playButton.setEnabled(hasAudio);
        
        // 更新播放按钮图标
        if (audioManager.isPlaying()) {
            binding.playButton.setImageResource(R.drawable.ic_pause);
            binding.playButton.setContentDescription(getString(R.string.pause));
        } else {
            binding.playButton.setImageResource(R.drawable.ic_play);
            binding.playButton.setContentDescription(getString(R.string.play));
        }

        // 更新录音按钮视觉反馈
        updateRecordButtonState();
    }
    
    /**
     * 更新录音按钮状态
     */
    private void updateRecordButtonState() {
        PageData currentPage = dayData.getPage(currentPagePosition + 1);
        boolean hasRecording = currentPage != null && currentPage.hasAudio();

        if (hasRecording) {
            // 已有录音，显示绿色图标
            binding.recordButton.setImageResource(R.drawable.ic_mic);
            binding.recordButton.setColorFilter(getResources().getColor(R.color.success_color, null));
        } else {
            // 无录音，显示默认图标
            binding.recordButton.setImageResource(R.drawable.ic_mic);
            binding.recordButton.setColorFilter(getResources().getColor(R.color.white, null));
        }
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(long milliseconds) {
        int seconds = (int) (milliseconds / 1000);
        int minutes = seconds / 60;
        seconds = seconds % 60;
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        if (audioManager != null) {
            audioManager.release();
        }
        if (binding != null) {
            binding = null;
        }
    }
    
    // AudioManager.AudioManagerListener 实现
    @Override
    public void onRecordingStarted() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                Toast.makeText(requireContext(), "开始录音", Toast.LENGTH_SHORT).show();
            });
        }
    }
    
    @Override
    public void onRecordingProgress(long duration) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                binding.recordingTimeText.setText(formatTime(duration));
            });
        }
    }
    
    @Override
    public void onRecordingStopped(String filePath, long duration) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // 更新数据仓库中的页面音频路径
                dataRepository.updatePageAudioPath(dayId, currentPagePosition + 1, filePath);

                // 确保页面数据正确更新
                PageData currentPage = dayData.getPage(currentPagePosition + 1);
                if (currentPage != null) {
                    currentPage.setAudioPath(filePath);
                    currentPage.setRecorded(true);
                    currentPage.setRecordingTime(duration);
                }

                Toast.makeText(requireContext(), "录音完成", Toast.LENGTH_SHORT).show();
                updateUI(); // 更新UI状态，启用下一个按钮和录音按钮视觉反馈
            });
        }
    }
    
    @Override
    public void onRecordingError(String error) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                Toast.makeText(requireContext(), error, Toast.LENGTH_SHORT).show();
                binding.recordingOverlay.setVisibility(View.GONE);
                isRecording = false;
            });
        }
    }
    
    @Override
    public void onPlaybackStarted() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(this::updateUI);
        }
    }
    
    @Override
    public void onPlaybackProgress(int currentPosition, int duration) {
        // 可以在这里更新播放进度
    }
    
    @Override
    public void onPlaybackPaused() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(this::updateUI);
        }
    }
    
    @Override
    public void onPlaybackCompleted() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(this::updateUI);
        }
    }
    
    @Override
    public void onPlaybackError(String error) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                Toast.makeText(requireContext(), error, Toast.LENGTH_SHORT).show();
                updateUI();
            });
        }
    }
}
