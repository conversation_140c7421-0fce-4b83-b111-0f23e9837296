package com.intensivereading.app.audio.recorder;

import android.content.Context;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.intensivereading.app.model.AudioState;
import com.intensivereading.app.utils.FileUtils;
import com.intensivereading.app.utils.PermissionUtils;

import java.io.IOException;

/**
 * 音频录制器
 */
public class AudioRecorder {
    private static final String TAG = "AudioRecorder";
    private static final int MIN_RECORDING_DURATION = 1000; // 最短录音时长1秒
    
    private Context context;
    private MediaRecorder mediaRecorder;
    private String currentFilePath;
    private AudioRecorderListener listener;
    private AudioState currentState;
    private Handler mainHandler;
    private long recordingStartTime;
    
    // 录音计时器
    private Runnable recordingTimer = new Runnable() {
        @Override
        public void run() {
            if (currentState == AudioState.RECORDING) {
                long duration = System.currentTimeMillis() - recordingStartTime;
                if (listener != null) {
                    listener.onRecordingProgress(duration);
                }
                mainHandler.postDelayed(this, 100); // 每100ms更新一次
            }
        }
    };
    
    public interface AudioRecorderListener {
        void onRecordingStarted();
        void onRecordingProgress(long duration);
        void onRecordingStopped(String filePath, long duration);
        void onRecordingError(String error);
    }
    
    public AudioRecorder(Context context) {
        this.context = context;
        this.currentState = AudioState.IDLE;
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public void setListener(AudioRecorderListener listener) {
        this.listener = listener;
    }
    
    /**
     * 开始录音
     */
    public boolean startRecording(String dayId, int pageNumber) {
        if (!PermissionUtils.hasAudioPermission(context)) {
            if (listener != null) {
                listener.onRecordingError("没有录音权限");
            }
            return false;
        }
        
        if (currentState != AudioState.IDLE) {
            Log.w(TAG, "录音器不在空闲状态，无法开始录音");
            return false;
        }
        
        try {
            // 生成音频文件路径
            currentFilePath = FileUtils.generateAudioPath(context, dayId, pageNumber);
            
            // 初始化MediaRecorder
            mediaRecorder = new MediaRecorder();
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
            mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS);
            mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
            mediaRecorder.setAudioSamplingRate(44100);
            mediaRecorder.setAudioEncodingBitRate(128000);
            mediaRecorder.setOutputFile(currentFilePath);
            
            mediaRecorder.prepare();
            mediaRecorder.start();
            
            currentState = AudioState.RECORDING;
            recordingStartTime = System.currentTimeMillis();
            
            // 开始计时器
            mainHandler.post(recordingTimer);
            
            if (listener != null) {
                listener.onRecordingStarted();
            }
            
            Log.d(TAG, "录音开始: " + currentFilePath);
            return true;
            
        } catch (IOException e) {
            Log.e(TAG, "录音开始失败", e);
            currentState = AudioState.ERROR;
            if (listener != null) {
                listener.onRecordingError("录音开始失败: " + e.getMessage());
            }
            cleanup();
            return false;
        }
    }
    
    /**
     * 停止录音
     */
    public boolean stopRecording() {
        if (currentState != AudioState.RECORDING) {
            Log.w(TAG, "录音器不在录音状态，无法停止录音");
            return false;
        }
        
        try {
            long recordingDuration = System.currentTimeMillis() - recordingStartTime;
            
            // 停止计时器
            mainHandler.removeCallbacks(recordingTimer);
            
            mediaRecorder.stop();
            mediaRecorder.release();
            mediaRecorder = null;
            
            currentState = AudioState.IDLE;
            
            // 检查录音时长
            if (recordingDuration < MIN_RECORDING_DURATION) {
                // 录音时间太短，删除文件，不显示提示信息
                FileUtils.deleteFile(currentFilePath);
                // 不调用listener.onRecordingError，直接静默取消
                return false;
            }
            
            if (listener != null) {
                listener.onRecordingStopped(currentFilePath, recordingDuration);
            }
            
            Log.d(TAG, "录音结束: " + currentFilePath + ", 时长: " + recordingDuration + "ms");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "录音停止失败", e);
            currentState = AudioState.ERROR;
            if (listener != null) {
                listener.onRecordingError("录音停止失败: " + e.getMessage());
            }
            cleanup();
            return false;
        }
    }
    
    /**
     * 取消录音
     */
    public void cancelRecording() {
        if (currentState == AudioState.RECORDING) {
            try {
                // 停止计时器
                mainHandler.removeCallbacks(recordingTimer);
                
                mediaRecorder.stop();
                mediaRecorder.release();
                mediaRecorder = null;
                
                // 删除录音文件
                if (currentFilePath != null) {
                    FileUtils.deleteFile(currentFilePath);
                }
                
                currentState = AudioState.IDLE;
                Log.d(TAG, "录音已取消");
                
            } catch (Exception e) {
                Log.e(TAG, "取消录音失败", e);
                cleanup();
            }
        }
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        if (mediaRecorder != null) {
            try {
                mediaRecorder.release();
            } catch (Exception e) {
                Log.e(TAG, "释放MediaRecorder失败", e);
            }
            mediaRecorder = null;
        }
        
        // 停止计时器
        mainHandler.removeCallbacks(recordingTimer);
        
        currentState = AudioState.IDLE;
        currentFilePath = null;
    }
    
    /**
     * 获取当前状态
     */
    public AudioState getCurrentState() {
        return currentState;
    }
    
    /**
     * 是否正在录音
     */
    public boolean isRecording() {
        return currentState == AudioState.RECORDING;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        cancelRecording();
        cleanup();
    }
}
