package com.intensivereading.app.utils;

import android.content.Context;
import android.media.MediaMetadataRetriever;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 文件操作工具类
 */
public class FileUtils {
    
    private static final String IMAGES_DIR = "images";
    private static final String AUDIO_DIR = "audio";
    private static final String AUDIO_PREFIX = "audio_";
    private static final String AUDIO_EXTENSION = ".aac";
    
    /**
     * 获取图片文件路径
     */
    public static String getImagePath(Context context, String dayId, int pageNumber) {
        File dayDir = new File(context.getFilesDir(), dayId);
        File imagesDir = new File(dayDir, IMAGES_DIR);
        
        // 创建目录如果不存在
        if (!imagesDir.exists()) {
            imagesDir.mkdirs();
        }
        
        return new File(imagesDir, "page_" + pageNumber + ".jpg").getAbsolutePath();
    }
    
    /**
     * 获取音频目录
     */
    public static File getAudioDir(Context context, String dayId) {
        File dayDir = new File(context.getFilesDir(), dayId);
        File audioDir = new File(dayDir, AUDIO_DIR);
        
        // 创建目录如果不存在
        if (!audioDir.exists()) {
            audioDir.mkdirs();
        }
        
        return audioDir;
    }
    
    /**
     * 生成新的音频文件路径
     */
    public static String generateAudioPath(Context context, String dayId, int pageNumber) {
        File audioDir = getAudioDir(context, dayId);
        
        // 生成时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String timestamp = sdf.format(new Date());
        
        String fileName = AUDIO_PREFIX + dayId + "_" + pageNumber + "_" + timestamp + AUDIO_EXTENSION;
        return new File(audioDir, fileName).getAbsolutePath();
    }
    
    /**
     * 获取页面最新的音频文件路径
     */
    public static String getLatestAudioPath(Context context, String dayId, int pageNumber) {
        File audioDir = getAudioDir(context, dayId);
        
        if (!audioDir.exists()) {
            return null;
        }
        
        File[] audioFiles = audioDir.listFiles((dir, name) -> 
            name.startsWith(AUDIO_PREFIX + dayId + "_" + pageNumber + "_") && 
            name.endsWith(AUDIO_EXTENSION)
        );
        
        if (audioFiles == null || audioFiles.length == 0) {
            return null;
        }
        
        // 找到最新的文件（按修改时间）
        File latestFile = audioFiles[0];
        for (File file : audioFiles) {
            if (file.lastModified() > latestFile.lastModified()) {
                latestFile = file;
            }
        }
        
        return latestFile.getAbsolutePath();
    }

    /**
     * 获取指定日期的所有音频文件
     */
    public static List<File> getAllAudioFiles(Context context, String dayId) {
        List<File> audioFiles = new ArrayList<>();
        File audioDir = getAudioDir(context, dayId);

        if (!audioDir.exists()) {
            return audioFiles;
        }

        File[] files = audioDir.listFiles((dir, name) ->
            name.startsWith(AUDIO_PREFIX + dayId + "_") &&
            name.endsWith(AUDIO_EXTENSION)
        );

        if (files != null) {
            for (File file : files) {
                if (file.isFile() && isValidAudioFile(file)) {
                    audioFiles.add(file);
                }
            }
        }

        return audioFiles;
    }

    /**
     * 扫描所有日期的音频文件
     */
    public static List<File> scanAllAudioFiles(Context context) {
        List<File> allAudioFiles = new ArrayList<>();
        String[] dayIds = {"monday", "tuesday", "wednesday", "thursday", "friday", "saturday"};

        for (String dayId : dayIds) {
            List<File> dayAudioFiles = getAllAudioFiles(context, dayId);
            allAudioFiles.addAll(dayAudioFiles);
        }

        return allAudioFiles;
    }

    /**
     * 验证音频文件是否有效
     */
    public static boolean isValidAudioFile(File audioFile) {
        if (!audioFile.exists() || !audioFile.isFile()) {
            return false;
        }

        // 检查文件大小（至少1KB）
        if (audioFile.length() < 1024) {
            return false;
        }

        // 尝试读取音频元数据
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            retriever.setDataSource(audioFile.getAbsolutePath());
            String duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            return duration != null && Long.parseLong(duration) > 0;
        } catch (Exception e) {
            return false;
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                // 忽略释放异常
            }
        }
    }

    /**
     * 检查文件是否存在
     */
    public static boolean fileExists(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
    
    /**
     * 删除文件
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
    
    /**
     * 获取文件大小
     */
    public static long getFileSize(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return 0;
        }
        File file = new File(filePath);
        return file.exists() ? file.length() : 0;
    }
    
    /**
     * 创建目录
     */
    public static boolean createDirectory(String dirPath) {
        if (dirPath == null || dirPath.isEmpty()) {
            return false;
        }
        File dir = new File(dirPath);
        return dir.exists() || dir.mkdirs();
    }

    /**
     * 获取音频文件时长（毫秒）
     */
    public static long getAudioDuration(String audioPath) {
        if (audioPath == null || audioPath.isEmpty()) {
            return 0;
        }

        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            retriever.setDataSource(audioPath);
            String durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            return durationStr != null ? Long.parseLong(durationStr) : 0;
        } catch (Exception e) {
            return 0;
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                // 忽略释放异常
            }
        }
    }

    /**
     * 从音频文件名中提取录音时间戳
     */
    public static long extractTimestampFromAudioPath(String audioPath) {
        if (audioPath == null || audioPath.isEmpty()) {
            return 0;
        }

        try {
            // 音频文件名格式: audio_dayId_pageNumber_timestamp.aac
            String fileName = new File(audioPath).getName();
            String[] parts = fileName.split("_");
            if (parts.length >= 4) {
                String timestampStr = parts[3].replace(AUDIO_EXTENSION, "");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
                Date date = sdf.parse(timestampStr);
                return date != null ? date.getTime() : 0;
            }
        } catch (Exception e) {
            // 解析失败，返回文件修改时间
            File file = new File(audioPath);
            return file.exists() ? file.lastModified() : 0;
        }
        return 0;
    }
}
