package com.intensivereading.app.audio.player;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.intensivereading.app.model.AudioState;
import com.intensivereading.app.utils.FileUtils;

import java.io.IOException;

/**
 * 音频播放器
 */
public class AudioPlayer implements MediaPlayer.OnCompletionListener, 
    MediaPlayer.OnErrorListener, MediaPlayer.OnPreparedListener {
    
    private static final String TAG = "AudioPlayer";
    private static final int PROGRESS_UPDATE_INTERVAL = 100; // 进度更新间隔100ms
    
    private Context context;
    private MediaPlayer mediaPlayer;
    private AudioPlayerListener listener;
    private AudioState currentState;
    private Handler mainHandler;
    private String currentFilePath;
    
    // 进度更新任务
    private Runnable progressUpdateTask = new Runnable() {
        @Override
        public void run() {
            if (mediaPlayer != null && currentState == AudioState.PLAYING) {
                try {
                    int currentPosition = mediaPlayer.getCurrentPosition();
                    int duration = mediaPlayer.getDuration();
                    
                    if (listener != null) {
                        listener.onPlaybackProgress(currentPosition, duration);
                    }
                    
                    mainHandler.postDelayed(this, PROGRESS_UPDATE_INTERVAL);
                } catch (IllegalStateException e) {
                    Log.e(TAG, "获取播放进度失败", e);
                }
            }
        }
    };
    
    public interface AudioPlayerListener {
        void onPlaybackStarted();
        void onPlaybackProgress(int currentPosition, int duration);
        void onPlaybackPaused();
        void onPlaybackCompleted();
        void onPlaybackError(String error);
    }
    
    public AudioPlayer(Context context) {
        this.context = context;
        this.currentState = AudioState.IDLE;
        this.mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public void setListener(AudioPlayerListener listener) {
        this.listener = listener;
    }
    
    /**
     * 播放音频文件
     */
    public boolean play(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            if (listener != null) {
                listener.onPlaybackError("文件路径为空");
            }
            return false;
        }
        
        if (!FileUtils.fileExists(filePath)) {
            if (listener != null) {
                listener.onPlaybackError("文件不存在: " + filePath);
            }
            return false;
        }
        
        // 如果正在播放同一个文件，则暂停/继续
        if (currentState == AudioState.PLAYING && filePath.equals(currentFilePath)) {
            pause();
            return true;
        } else if (currentState == AudioState.PAUSED && filePath.equals(currentFilePath)) {
            resume();
            return true;
        }
        
        // 停止当前播放
        stop();
        
        try {
            mediaPlayer = new MediaPlayer();
            mediaPlayer.setOnCompletionListener(this);
            mediaPlayer.setOnErrorListener(this);
            mediaPlayer.setOnPreparedListener(this);
            
            mediaPlayer.setDataSource(filePath);
            mediaPlayer.prepareAsync();
            
            currentFilePath = filePath;
            currentState = AudioState.IDLE; // 准备中
            
            Log.d(TAG, "开始准备播放: " + filePath);
            return true;
            
        } catch (IOException e) {
            Log.e(TAG, "播放失败", e);
            currentState = AudioState.ERROR;
            if (listener != null) {
                listener.onPlaybackError("播放失败: " + e.getMessage());
            }
            cleanup();
            return false;
        }
    }
    
    /**
     * 暂停播放
     */
    public void pause() {
        if (mediaPlayer != null && currentState == AudioState.PLAYING) {
            try {
                mediaPlayer.pause();
                currentState = AudioState.PAUSED;
                
                // 停止进度更新
                mainHandler.removeCallbacks(progressUpdateTask);
                
                if (listener != null) {
                    listener.onPlaybackPaused();
                }
                
                Log.d(TAG, "播放已暂停");
            } catch (IllegalStateException e) {
                Log.e(TAG, "暂停播放失败", e);
            }
        }
    }
    
    /**
     * 继续播放
     */
    public void resume() {
        if (mediaPlayer != null && currentState == AudioState.PAUSED) {
            try {
                mediaPlayer.start();
                currentState = AudioState.PLAYING;
                
                // 开始进度更新
                mainHandler.post(progressUpdateTask);
                
                if (listener != null) {
                    listener.onPlaybackStarted();
                }
                
                Log.d(TAG, "播放已继续");
            } catch (IllegalStateException e) {
                Log.e(TAG, "继续播放失败", e);
            }
        }
    }
    
    /**
     * 停止播放
     */
    public void stop() {
        if (mediaPlayer != null) {
            try {
                if (currentState == AudioState.PLAYING || currentState == AudioState.PAUSED) {
                    mediaPlayer.stop();
                }
                
                // 停止进度更新
                mainHandler.removeCallbacks(progressUpdateTask);
                
                Log.d(TAG, "播放已停止");
            } catch (IllegalStateException e) {
                Log.e(TAG, "停止播放失败", e);
            }
            
            cleanup();
        }
    }
    
    /**
     * 跳转到指定位置
     */
    public void seekTo(int position) {
        if (mediaPlayer != null && (currentState == AudioState.PLAYING || currentState == AudioState.PAUSED)) {
            try {
                mediaPlayer.seekTo(position);
                Log.d(TAG, "跳转到位置: " + position);
            } catch (IllegalStateException e) {
                Log.e(TAG, "跳转失败", e);
            }
        }
    }
    
    /**
     * 获取当前播放位置
     */
    public int getCurrentPosition() {
        if (mediaPlayer != null && (currentState == AudioState.PLAYING || currentState == AudioState.PAUSED)) {
            try {
                return mediaPlayer.getCurrentPosition();
            } catch (IllegalStateException e) {
                Log.e(TAG, "获取当前位置失败", e);
            }
        }
        return 0;
    }
    
    /**
     * 获取音频总时长
     */
    public int getDuration() {
        if (mediaPlayer != null && (currentState == AudioState.PLAYING || currentState == AudioState.PAUSED)) {
            try {
                return mediaPlayer.getDuration();
            } catch (IllegalStateException e) {
                Log.e(TAG, "获取音频时长失败", e);
            }
        }
        return 0;
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        if (mediaPlayer != null) {
            try {
                mediaPlayer.release();
            } catch (Exception e) {
                Log.e(TAG, "释放MediaPlayer失败", e);
            }
            mediaPlayer = null;
        }
        
        // 停止进度更新
        mainHandler.removeCallbacks(progressUpdateTask);
        
        currentState = AudioState.IDLE;
        currentFilePath = null;
    }
    
    /**
     * 获取当前状态
     */
    public AudioState getCurrentState() {
        return currentState;
    }
    
    /**
     * 是否正在播放
     */
    public boolean isPlaying() {
        return currentState == AudioState.PLAYING;
    }
    
    /**
     * 是否已暂停
     */
    public boolean isPaused() {
        return currentState == AudioState.PAUSED;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stop();
        cleanup();
    }
    
    // MediaPlayer.OnPreparedListener 实现
    @Override
    public void onPrepared(MediaPlayer mp) {
        Log.d(TAG, "音频准备完成，开始播放");
        try {
            mp.start();
            currentState = AudioState.PLAYING;
            
            // 开始进度更新
            mainHandler.post(progressUpdateTask);
            
            if (listener != null) {
                listener.onPlaybackStarted();
            }
        } catch (IllegalStateException e) {
            Log.e(TAG, "开始播放失败", e);
            currentState = AudioState.ERROR;
            if (listener != null) {
                listener.onPlaybackError("开始播放失败: " + e.getMessage());
            }
        }
    }
    
    // MediaPlayer.OnCompletionListener 实现
    @Override
    public void onCompletion(MediaPlayer mp) {
        Log.d(TAG, "播放完成");
        
        // 停止进度更新
        mainHandler.removeCallbacks(progressUpdateTask);
        
        currentState = AudioState.IDLE;
        
        if (listener != null) {
            listener.onPlaybackCompleted();
        }
        
        cleanup();
    }
    
    // MediaPlayer.OnErrorListener 实现
    @Override
    public boolean onError(MediaPlayer mp, int what, int extra) {
        Log.e(TAG, "播放错误: what=" + what + ", extra=" + extra);
        
        currentState = AudioState.ERROR;
        
        if (listener != null) {
            listener.onPlaybackError("播放错误: " + what + "/" + extra);
        }
        
        cleanup();
        return true; // 表示错误已处理
    }
}
