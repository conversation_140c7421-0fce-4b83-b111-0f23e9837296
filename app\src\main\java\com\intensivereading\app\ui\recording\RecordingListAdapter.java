package com.intensivereading.app.ui.recording;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.intensivereading.app.R;
import com.intensivereading.app.model.RecordingData;

import java.util.List;

/**
 * 录音记录列表适配器
 */
public class RecordingListAdapter extends RecyclerView.Adapter<RecordingListAdapter.RecordingViewHolder> {
    
    private Context context;
    private List<RecordingData> recordingList;
    private OnRecordingClickListener listener;
    private RecordingData currentPlayingRecording;
    private boolean isPlaying = false;

    public interface OnRecordingClickListener {
        void onPlayClick(RecordingData recording);
        void onPauseClick(RecordingData recording);
    }
    
    public RecordingListAdapter(Context context, List<RecordingData> recordingList) {
        this.context = context;
        this.recordingList = recordingList;
    }
    
    public void setOnRecordingClickListener(OnRecordingClickListener listener) {
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public RecordingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recording, parent, false);
        return new RecordingViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecordingViewHolder holder, int position) {
        RecordingData recording = recordingList.get(position);
        holder.bind(recording);
    }
    
    @Override
    public int getItemCount() {
        return recordingList.size();
    }
    
    /**
     * 更新数据
     */
    public void updateData(List<RecordingData> newRecordingList) {
        this.recordingList = newRecordingList;
        notifyDataSetChanged();
    }

    /**
     * 设置当前播放的录音
     */
    public void setCurrentPlayingRecording(RecordingData recording, boolean playing) {
        RecordingData previousPlaying = this.currentPlayingRecording;
        this.currentPlayingRecording = recording;
        this.isPlaying = playing;

        // 刷新之前播放的项目
        if (previousPlaying != null) {
            int previousIndex = recordingList.indexOf(previousPlaying);
            if (previousIndex >= 0) {
                notifyItemChanged(previousIndex);
            }
        }

        // 刷新当前播放的项目
        if (recording != null) {
            int currentIndex = recordingList.indexOf(recording);
            if (currentIndex >= 0) {
                notifyItemChanged(currentIndex);
            }
        }
    }

    /**
     * 清除播放状态
     */
    public void clearPlayingState() {
        setCurrentPlayingRecording(null, false);
    }
    
    /**
     * ViewHolder类
     */
    class RecordingViewHolder extends RecyclerView.ViewHolder {
        private TextView statusIcon;
        private TextView titleText;
        private TextView startTimeText;
        private TextView durationText;
        private ImageButton playButton;
        
        public RecordingViewHolder(@NonNull View itemView) {
            super(itemView);
            statusIcon = itemView.findViewById(R.id.statusIcon);
            titleText = itemView.findViewById(R.id.titleText);
            startTimeText = itemView.findViewById(R.id.startTimeText);
            durationText = itemView.findViewById(R.id.durationText);
            playButton = itemView.findViewById(R.id.playButton);
        }
        
        public void bind(RecordingData recording) {
            // 设置标题
            titleText.setText(recording.getDisplayTitle());
            
            // 设置录音状态图标
            statusIcon.setText(recording.getStatusIcon());
            if (recording.hasRecording()) {
                statusIcon.setTextColor(context.getResources().getColor(R.color.success_color, null));
            } else {
                statusIcon.setTextColor(context.getResources().getColor(R.color.text_hint, null));
            }
            
            // 设置开始时间
            startTimeText.setText(recording.getFormattedStartTime());
            
            // 设置录音时长
            if (recording.hasRecording()) {
                durationText.setText(context.getString(R.string.duration_format, recording.getFormattedDuration()));
                durationText.setVisibility(View.VISIBLE);
            } else {
                durationText.setVisibility(View.GONE);
            }
            
            // 设置播放按钮状态
            playButton.setEnabled(recording.hasRecording());
            if (recording.hasRecording()) {
                playButton.setAlpha(1.0f);

                // 检查是否是当前播放的录音
                boolean isCurrentPlaying = recording.equals(currentPlayingRecording);

                if (isCurrentPlaying && isPlaying) {
                    // 当前正在播放，显示暂停图标
                    playButton.setImageResource(R.drawable.ic_pause);
                    playButton.setOnClickListener(v -> {
                        if (listener != null) {
                            listener.onPauseClick(recording);
                        }
                    });
                } else {
                    // 未播放或已暂停，显示播放图标
                    playButton.setImageResource(R.drawable.ic_play);
                    playButton.setOnClickListener(v -> {
                        if (listener != null) {
                            listener.onPlayClick(recording);
                        }
                    });
                }
            } else {
                playButton.setAlpha(0.3f);
                playButton.setImageResource(R.drawable.ic_play);
                playButton.setOnClickListener(null);
            }
        }
    }
}
