package com.intensivereading.app.ui.main;

import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;

import com.intensivereading.app.R;
import com.intensivereading.app.databinding.FragmentMainBinding;
import com.intensivereading.app.repository.DataRepository;
import com.intensivereading.app.utils.PermissionUtils;

/**
 * 主界面Fragment
 */
public class MainFragment extends Fragment {
    
    private FragmentMainBinding binding;
    private DataRepository dataRepository;
    
    // 权限请求启动器
    private ActivityResultLauncher<String[]> permissionLauncher = 
        registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), 
            result -> {
                boolean allGranted = true;
                for (Boolean granted : result.values()) {
                    if (!granted) {
                        allGranted = false;
                        break;
                    }
                }
                
                if (allGranted) {
                    Toast.makeText(requireContext(), "权限已授予", Toast.LENGTH_SHORT).show();
                } else {
                    showPermissionDeniedDialog();
                }
            });
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化数据仓库
        dataRepository = DataRepository.getInstance(requireContext());
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        binding = FragmentMainBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Debug: Check if binding is null
        if (binding == null) {
            android.util.Log.e("MainFragment", "Binding is null!");
            return;
        }

        android.util.Log.d("MainFragment", "Binding is not null, setting up listeners");

        // 设置按钮点击事件
        try {
            setupButtonClickListeners();
        } catch (Exception e) {
            android.util.Log.e("MainFragment", "Error in setupButtonClickListeners", e);
        }

        // 检查权限
        checkPermissions();
    }
    
    /**
     * 设置按钮点击事件
     */
    private void setupButtonClickListeners() {
        // Debug: Check each button individually
        android.util.Log.d("MainFragment", "Setting up button listeners");

        // Try to find buttons manually as a test
        View mondayView = requireView().findViewById(R.id.mondayButton);
        View recordingView = requireView().findViewById(R.id.recordingListButton);
        android.util.Log.d("MainFragment", "Manual findViewById - monday: " + (mondayView != null) + ", recording: " + (recordingView != null));

        // Set up all day buttons with fallback
        setupDayButton("monday", R.id.mondayButton, binding.mondayButton);
        setupDayButton("tuesday", R.id.tuesdayButton, binding.tuesdayButton);
        setupDayButton("wednesday", R.id.wednesdayButton, binding.wednesdayButton);
        setupDayButton("thursday", R.id.thursdayButton, binding.thursdayButton);
        setupDayButton("friday", R.id.fridayButton, binding.fridayButton);
        setupDayButton("saturday", R.id.saturdayButton, binding.saturdayButton);

        // 学习记录按钮
        if (binding.recordingListButton == null) {
            android.util.Log.e("MainFragment", "recordingListButton is null! Trying findViewById fallback");
            // Fallback to findViewById
            View recordingButton = requireView().findViewById(R.id.recordingListButton);
            if (recordingButton != null) {
                recordingButton.setOnClickListener(v -> navigateToRecordingList());
                android.util.Log.d("MainFragment", "recordingListButton listener set via findViewById");
            } else {
                android.util.Log.e("MainFragment", "recordingListButton not found even with findViewById!");
            }
        } else {
            binding.recordingListButton.setOnClickListener(v -> navigateToRecordingList());
            android.util.Log.d("MainFragment", "recordingListButton listener set");
        }

        android.util.Log.d("MainFragment", "Button listener setup completed");
    }

    /**
     * 设置单个日期按钮的点击事件，带有findViewById fallback
     */
    private void setupDayButton(String dayId, int buttonId, View bindingButton) {
        if (bindingButton == null) {
            android.util.Log.e("MainFragment", dayId + "Button is null! Trying findViewById fallback");
            View fallbackButton = requireView().findViewById(buttonId);
            if (fallbackButton != null) {
                fallbackButton.setOnClickListener(v -> navigateToDetail(dayId));
                android.util.Log.d("MainFragment", dayId + "Button listener set via findViewById");
            } else {
                android.util.Log.e("MainFragment", dayId + "Button not found even with findViewById!");
            }
        } else {
            bindingButton.setOnClickListener(v -> navigateToDetail(dayId));
            android.util.Log.d("MainFragment", dayId + "Button listener set");
        }
    }

    /**
     * 导航到详情页面
     */
    private void navigateToDetail(String dayId) {
        Bundle args = new Bundle();
        args.putString("dayId", dayId);
        Navigation.findNavController(requireView()).navigate(R.id.action_main_to_detail, args);
    }

    /**
     * 导航到学习记录页面
     */
    private void navigateToRecordingList() {
        Navigation.findNavController(requireView())
                .navigate(R.id.action_main_to_recording_list);
    }

    /**
     * 检查权限
     */
    private void checkPermissions() {
        if (!PermissionUtils.hasAllPermissions(requireContext())) {
            String[] missingPermissions = PermissionUtils.getMissingPermissions(requireContext());
            if (missingPermissions.length > 0) {
                showPermissionRequestDialog(missingPermissions);
            }
        }
    }
    
    /**
     * 显示权限请求对话框
     */
    private void showPermissionRequestDialog(String[] permissions) {
        new AlertDialog.Builder(requireContext())
            .setTitle(R.string.permission_audio_title)
            .setMessage("应用需要录音和存储权限来正常工作")
            .setPositiveButton(R.string.grant_permission, (dialog, which) -> {
                permissionLauncher.launch(permissions);
            })
            .setNegativeButton(R.string.cancel, (dialog, which) -> {
                showPermissionDeniedDialog();
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * 显示权限被拒绝对话框
     */
    private void showPermissionDeniedDialog() {
        new AlertDialog.Builder(requireContext())
            .setTitle(R.string.permission_denied)
            .setMessage(R.string.error_permission_required)
            .setPositiveButton("确定", (dialog, which) -> {
                // 可以引导用户到设置页面
            })
            .show();
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
