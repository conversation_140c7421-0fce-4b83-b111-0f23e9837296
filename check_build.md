# 构建检查清单

## 构建前检查

### 1. 环境要求
- [ ] Android Studio Arctic Fox 或更高版本
- [ ] JDK 8 或更高版本
- [ ] Android SDK API 31-34
- [ ] Gradle 8.0+

### 2. 项目结构检查
- [ ] 所有Java文件编译无错误
- [ ] 资源文件完整（布局、字符串、图标等）
- [ ] AndroidManifest.xml配置正确
- [ ] build.gradle依赖配置正确

### 3. 权限配置检查
- [ ] RECORD_AUDIO权限已声明
- [ ] READ_EXTERNAL_STORAGE权限已声明
- [ ] WRITE_EXTERNAL_STORAGE权限已声明

## 功能测试清单

### 1. 主界面测试
- [ ] 应用正常启动
- [ ] 6个日期按钮正常显示
- [ ] 权限请求对话框正常弹出
- [ ] 权限授予后功能正常

### 2. 导航测试
- [ ] 点击日期按钮能正常跳转到详情页
- [ ] 详情页返回按钮功能正常
- [ ] Fragment间切换动画正常

### 3. 详情页面测试
- [ ] ViewPager2正常显示图片页面
- [ ] 页面指示器显示正确
- [ ] 左右滑动切换页面正常

### 4. 音频功能测试
- [ ] 长按录音按钮开始录音
- [ ] 录音状态覆盖层正常显示
- [ ] 录音时长计时器正常工作
- [ ] 松开按钮停止录音
- [ ] 录音文件正常保存
- [ ] 播放按钮播放音频正常
- [ ] 播放/暂停状态切换正常

### 5. 页面控制测试
- [ ] "上一个"按钮在第一页时禁用
- [ ] "下一个"按钮在未录音时禁用
- [ ] 录音完成后"下一个"按钮启用
- [ ] 页面切换时音频播放自动停止

## 性能测试

### 1. 内存使用
- [ ] 应用启动内存使用合理
- [ ] 长时间使用无明显内存泄漏
- [ ] 图片加载使用Glide缓存

### 2. 响应性能
- [ ] UI操作响应及时（<100ms）
- [ ] ViewPager2切换流畅（60fps）
- [ ] 音频播放启动延迟<100ms

### 3. 文件管理
- [ ] 音频文件正确保存到内部存储
- [ ] 文件命名规则正确
- [ ] 重复录音时旧文件处理正确

## 错误处理测试

### 1. 权限处理
- [ ] 权限被拒绝时显示友好提示
- [ ] 权限不足时功能正确禁用

### 2. 音频错误处理
- [ ] 录音失败时显示错误信息
- [ ] 播放失败时显示错误信息
- [ ] 文件不存在时处理正确

### 3. 异常情况
- [ ] 设备旋转时状态保持正确
- [ ] 应用后台恢复时功能正常
- [ ] 低存储空间时处理正确

## 兼容性测试

### 1. Android版本
- [ ] Android 12 (API 31) 正常运行
- [ ] Android 13 (API 33) 正常运行
- [ ] Android 14 (API 34) 正常运行

### 2. 设备类型
- [ ] 手机设备正常运行
- [ ] 平板设备布局适配
- [ ] 不同屏幕尺寸适配

## 构建命令

### Debug构建
```bash
./gradlew assembleDebug
```

### Release构建
```bash
./gradlew assembleRelease
```

### 运行测试
```bash
./gradlew test
```

### 安装到设备
```bash
./gradlew installDebug
```

## 常见问题解决

### 1. 编译错误
- 检查Java版本兼容性
- 清理并重新构建项目
- 同步Gradle依赖

### 2. 运行时错误
- 检查权限配置
- 查看Logcat日志
- 验证文件路径正确性

### 3. 性能问题
- 使用Android Profiler分析
- 检查内存泄漏
- 优化图片加载策略
