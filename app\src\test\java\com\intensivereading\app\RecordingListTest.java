package com.intensivereading.app;

import android.content.Context;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.intensivereading.app.model.RecordingData;
import com.intensivereading.app.repository.DataRepository;
import com.intensivereading.app.utils.FileUtils;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.File;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 学习记录功能测试
 */
@RunWith(AndroidJUnit4.class)
public class RecordingListTest {

    private Context context;
    private DataRepository dataRepository;

    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        dataRepository = DataRepository.getInstance(context);
    }

    @Test
    public void testGetAllRecordingData() {
        // 测试获取所有录音数据
        List<RecordingData> recordings = dataRepository.getAllRecordingData();
        assertNotNull("录音数据列表不应为null", recordings);
        
        // 验证只包含周一到周六的数据
        for (RecordingData recording : recordings) {
            String dayId = recording.getDayId();
            assertNotEquals("不应包含周日数据", "sunday", dayId);
            assertTrue("应该是有效的工作日", 
                dayId.equals("monday") || dayId.equals("tuesday") || 
                dayId.equals("wednesday") || dayId.equals("thursday") || 
                dayId.equals("friday") || dayId.equals("saturday"));
        }
    }

    @Test
    public void testRecordingDataFormatting() {
        // 测试录音数据格式化
        RecordingData recording = new RecordingData("monday", "周一", 1);
        
        // 测试显示标题
        assertEquals("显示标题格式正确", "周一 - 第1页", recording.getDisplayTitle());
        
        // 测试时长格式化
        recording.setRecordingTime(65000); // 1分5秒
        assertEquals("时长格式化正确", "01:05", recording.getFormattedDuration());
        
        recording.setRecordingTime(3665000); // 1小时1分5秒
        assertEquals("长时长格式化正确", "01:01:05", recording.getFormattedDuration());
        
        // 测试状态图标
        assertFalse("初始状态无录音", recording.hasRecording());
        assertEquals("无录音状态图标", "✗", recording.getStatusIcon());
        
        recording.setHasRecording(true);
        assertTrue("设置有录音", recording.hasRecording());
        assertEquals("有录音状态图标", "✓", recording.getStatusIcon());
    }

    @Test
    public void testAudioFileValidation() {
        // 测试音频文件验证功能
        File nonExistentFile = new File("/non/existent/path.aac");
        assertFalse("不存在的文件应该无效", FileUtils.isValidAudioFile(nonExistentFile));
    }

    @Test
    public void testAudioFileScanning() {
        // 测试音频文件扫描功能
        List<File> audioFiles = FileUtils.scanAllAudioFiles(context);
        assertNotNull("音频文件列表不应为null", audioFiles);
        
        // 验证扫描的文件都是有效的
        for (File audioFile : audioFiles) {
            assertTrue("扫描的音频文件应该有效", FileUtils.isValidAudioFile(audioFile));
        }
    }

    @Test
    public void testDataRepositoryRefresh() {
        // 测试数据仓库刷新功能
        try {
            dataRepository.refreshAudioData();
            dataRepository.validateAudioFiles();
            // 如果没有异常，测试通过
            assertTrue("数据刷新和验证成功", true);
        } catch (Exception e) {
            fail("数据刷新和验证不应抛出异常: " + e.getMessage());
        }
    }
}
