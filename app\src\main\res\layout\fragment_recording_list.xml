<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.recording.RecordingListFragment">

    <!-- 左上角返回按钮 -->
    <ImageButton
        android:id="@+id/backButton"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_margin="12dp"
        android:background="@drawable/circle_background"
        android:contentDescription="@string/back"
        android:src="@drawable/ic_arrow_back"
        android:scaleType="centerInside"
        android:elevation="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/recording_list"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 录音记录列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recordingRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="12dp"
        android:clipToPadding="false"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleText"
        tools:listitem="@layout/item_recording" />

    <!-- 空状态提示 -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleText">

        <ImageView
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_marginBottom="12dp"
            android:alpha="0.5"
            android:src="@drawable/ic_mic"
            android:tint="@color/text_hint" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_recordings"
            android:textColor="@color/text_hint"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="@string/no_recordings_hint"
            android:textColor="@color/text_hint"
            android:textSize="12sp"
            android:gravity="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
