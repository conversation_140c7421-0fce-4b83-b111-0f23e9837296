package com.intensivereading.app.ui.detail;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.intensivereading.app.R;
import com.intensivereading.app.model.PageData;
import com.intensivereading.app.utils.FileUtils;

import java.util.List;

/**
 * ViewPager2的图片适配器
 */
public class ImagePagerAdapter extends RecyclerView.Adapter<ImagePagerAdapter.ImageViewHolder> {
    
    private Context context;
    private List<PageData> pages;
    
    public ImagePagerAdapter(Context context, List<PageData> pages) {
        this.context = context;
        this.pages = pages;
    }
    
    @NonNull
    @Override
    public ImageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_image_page, parent, false);
        return new ImageViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ImageViewHolder holder, int position) {
        PageData pageData = pages.get(position);
        holder.bind(pageData);
    }
    
    @Override
    public int getItemCount() {
        return pages.size();
    }
    
    /**
     * 更新数据
     */
    public void updateData(List<PageData> newPages) {
        this.pages = newPages;
        notifyDataSetChanged();
    }
    
    /**
     * ViewHolder类
     */
    class ImageViewHolder extends RecyclerView.ViewHolder {
        private ImageView imageView;
        private LinearLayout placeholderLayout;
        
        public ImageViewHolder(@NonNull View itemView) {
            super(itemView);
            imageView = itemView.findViewById(R.id.imageView);
            placeholderLayout = itemView.findViewById(R.id.placeholderLayout);
        }
        
        public void bind(PageData pageData) {
            String imagePath = pageData.getImagePath();
            
            // 检查图片文件是否存在
            if (imagePath != null && FileUtils.fileExists(imagePath)) {
                // 显示图片
                placeholderLayout.setVisibility(View.GONE);
                imageView.setVisibility(View.VISIBLE);
                
                // 使用Glide加载图片
                Glide.with(context)
                    .load(imagePath)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .placeholder(R.drawable.ic_calendar)
                    .error(R.drawable.ic_calendar)
                    .into(imageView);
                    
            } else {
                // 显示占位符
                imageView.setVisibility(View.GONE);
                placeholderLayout.setVisibility(View.VISIBLE);
            }
        }
    }
}
