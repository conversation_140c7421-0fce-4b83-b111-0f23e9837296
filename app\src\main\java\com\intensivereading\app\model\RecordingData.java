package com.intensivereading.app.model;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 录音记录数据模型
 */
public class RecordingData {
    private String dayId;           // 日期ID (monday, tuesday, etc.)
    private String dayName;         // 日期显示名称 (周一, 周二, etc.)
    private int pageNumber;         // 页面编号
    private boolean hasRecording;   // 是否有录音
    private String audioPath;       // 音频文件路径
    private long recordingTime;     // 录音时长（毫秒）
    private long startTime;         // 录音开始时间（时间戳）
    
    public RecordingData(String dayId, String dayName, int pageNumber) {
        this.dayId = dayId;
        this.dayName = dayName;
        this.pageNumber = pageNumber;
        this.hasRecording = false;
        this.recordingTime = 0;
        this.startTime = 0;
    }
    
    // Getters and Setters
    public String getDayId() {
        return dayId;
    }
    
    public void setDayId(String dayId) {
        this.dayId = dayId;
    }
    
    public String getDayName() {
        return dayName;
    }
    
    public void setDayName(String dayName) {
        this.dayName = dayName;
    }
    
    public int getPageNumber() {
        return pageNumber;
    }
    
    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    public boolean hasRecording() {
        return hasRecording;
    }
    
    public void setHasRecording(boolean hasRecording) {
        this.hasRecording = hasRecording;
    }
    
    public String getAudioPath() {
        return audioPath;
    }
    
    public void setAudioPath(String audioPath) {
        this.audioPath = audioPath;
        this.hasRecording = (audioPath != null && !audioPath.isEmpty());
    }
    
    public long getRecordingTime() {
        return recordingTime;
    }
    
    public void setRecordingTime(long recordingTime) {
        this.recordingTime = recordingTime;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    /**
     * 获取格式化的开始时间
     * @return 格式化的时间字符串 (YYYY-MM-DD HH:mm:ss)
     */
    public String getFormattedStartTime() {
        if (startTime == 0) {
            return "未录音";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date(startTime));
    }
    
    /**
     * 获取格式化的录音时长
     * @return 格式化的时长字符串 (mm:ss 或 HH:mm:ss)
     */
    public String getFormattedDuration() {
        if (recordingTime == 0) {
            return "00:00";
        }
        int totalSeconds = (int) (recordingTime / 1000);
        int hours = totalSeconds / 3600;
        int minutes = (totalSeconds % 3600) / 60;
        int seconds = totalSeconds % 60;

        if (hours > 0) {
            return String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
        }
    }
    
    /**
     * 获取录音状态图标资源ID
     * @return 图标资源ID
     */
    public String getStatusIcon() {
        return hasRecording ? "✓" : "✗";
    }
    
    /**
     * 获取完整的显示标题
     * @return 显示标题 (例如: "周一 - 第1页")
     */
    public String getDisplayTitle() {
        return dayName + " - 第" + pageNumber + "页";
    }
}
