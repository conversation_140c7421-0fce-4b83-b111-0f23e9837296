package com.intensivereading.app.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

/**
 * 权限管理工具类
 */
public class PermissionUtils {
    
    // 权限请求码
    public static final int REQUEST_AUDIO_PERMISSION = 1001;
    public static final int REQUEST_STORAGE_PERMISSION = 1002;
    public static final int REQUEST_ALL_PERMISSIONS = 1003;
    
    // 需要的权限
    public static final String[] REQUIRED_PERMISSIONS = {
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    };
    
    /**
     * 检查是否有录音权限
     */
    public static boolean hasAudioPermission(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 检查是否有存储权限
     */
    public static boolean hasStoragePermission(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
                == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 检查是否有所有必需权限
     */
    public static boolean hasAllPermissions(Context context) {
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) 
                    != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 请求录音权限
     */
    public static void requestAudioPermission(Activity activity) {
        ActivityCompat.requestPermissions(activity, 
            new String[]{Manifest.permission.RECORD_AUDIO}, 
            REQUEST_AUDIO_PERMISSION);
    }
    
    /**
     * 请求存储权限
     */
    public static void requestStoragePermission(Activity activity) {
        ActivityCompat.requestPermissions(activity, 
            new String[]{
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            }, 
            REQUEST_STORAGE_PERMISSION);
    }
    
    /**
     * 请求所有必需权限
     */
    public static void requestAllPermissions(Activity activity) {
        ActivityCompat.requestPermissions(activity, REQUIRED_PERMISSIONS, REQUEST_ALL_PERMISSIONS);
    }
    
    /**
     * 获取缺失的权限
     */
    public static String[] getMissingPermissions(Context context) {
        java.util.List<String> missingPermissions = new java.util.ArrayList<>();
        
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) 
                    != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }
        
        return missingPermissions.toArray(new String[0]);
    }
    
    /**
     * 检查权限请求结果
     */
    public static boolean isPermissionGranted(int[] grantResults) {
        if (grantResults.length == 0) {
            return false;
        }
        
        for (int result : grantResults) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        
        return true;
    }
}
